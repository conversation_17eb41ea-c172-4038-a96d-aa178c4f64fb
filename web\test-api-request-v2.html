<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ApiRequestV2 组件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 8px;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
            margin-top: 15px;
        }
        .expected-result h4 {
            margin: 0 0 10px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">ApiRequestV2 组件问题修复测试</div>
        <div class="test-description">
            本测试用于验证 ApiRequestV2 组件中的两个问题是否已经修复：
            <br>1. 刷新后请求头参数重复的问题
            <br>2. result 刷新后才出现的问题
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">测试1：刷新后请求头参数重复问题</div>
        <div class="test-description">
            测试在页面刷新后，请求头、参数、请求体等配置是否会出现重复项。
        </div>
        <div class="test-steps">
            <h4>测试步骤：</h4>
            <ol>
                <li>打开工作流编辑器，添加一个 ApiRequestV2 动作</li>
                <li>选择一个已配置模板的API（确保该API有请求头、参数、请求体模板）</li>
                <li>观察加载的模板数据，记录请求头、参数、请求体的数量</li>
                <li>刷新浏览器页面（F5 或 Ctrl+R）</li>
                <li>重新打开同一个 ApiRequestV2 动作</li>
                <li>观察请求头、参数、请求体的数量是否与刷新前一致</li>
            </ol>
        </div>
        <div class="expected-result">
            <h4>预期结果：</h4>
            <ul>
                <li>刷新前后，请求头、参数、请求体的数量应该保持一致</li>
                <li>不应该出现重复的配置项</li>
                <li>控制台应该显示 "Skipping API change during initialization" 日志，表明初始化期间跳过了API变化处理</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">测试2：result 刷新后才出现问题</div>
        <div class="test-description">
            测试在组件初始化时，输出参数（result）部分是否能立即显示，而不需要等到刷新后才出现。
        </div>
        <div class="test-steps">
            <h4>测试步骤：</h4>
            <ol>
                <li>打开工作流编辑器，添加一个 ApiRequestV2 动作</li>
                <li>选择一个已配置响应体模板的API</li>
                <li>立即观察"输出参数"部分是否显示了响应体结构</li>
                <li>如果没有显示，等待几秒钟观察是否会自动出现</li>
                <li>刷新页面后重新测试，确认结果一致性</li>
            </ol>
        </div>
        <div class="expected-result">
            <h4>预期结果：</h4>
            <ul>
                <li>选择API后，输出参数部分应该立即或在很短时间内（1-2秒）显示响应体结构</li>
                <li>不需要刷新页面就能看到完整的输出参数配置</li>
                <li>控制台应该显示相关的模板加载成功日志</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">测试3：初始化流程验证</div>
        <div class="test-description">
            验证组件初始化流程是否按预期工作，避免重复加载和竞态条件。
        </div>
        <div class="test-steps">
            <h4>测试步骤：</h4>
            <ol>
                <li>打开浏览器开发者工具，切换到 Console 标签</li>
                <li>打开工作流编辑器，添加一个 ApiRequestV2 动作</li>
                <li>选择一个API，观察控制台日志</li>
                <li>刷新页面，重新打开同一个动作，再次观察控制台日志</li>
            </ol>
        </div>
        <div class="expected-result">
            <h4>预期结果：</h4>
            <ul>
                <li>控制台应该显示清晰的初始化流程日志</li>
                <li>应该看到 "开始初始化模板" 和 "模板初始化完成" 日志</li>
                <li>在初始化期间，应该看到 "Skipping API change during initialization" 日志</li>
                <li>不应该有重复的模板加载日志</li>
            </ul>
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">修复说明</div>
        <div class="test-description">
            <strong>问题1修复：</strong>
            <br>• 添加了 hasInitialized 标记，确保初始化完成前不处理API变化事件
            <br>• 在 store 中添加了 shouldLoadApiTemplate() 检查，避免重复加载已有数据
            <br>• 优化了初始化时序，使用 setTimeout 确保异步操作完成
            <br><br>
            <strong>问题2修复：</strong>
            <br>• 在 updateResponseTemplate 和 clearResponseTemplate 方法中添加了立即更新结果数据的调用
            <br>• 在初始化响应体模板加载成功后立即调用 updateResultData()
            <br>• 优化了 setArgs 方法，分离出独立的 updateResultData 方法
            <br>• 确保响应体模板加载完成后立即反映到UI上
        </div>
    </div>
</body>
</html>
