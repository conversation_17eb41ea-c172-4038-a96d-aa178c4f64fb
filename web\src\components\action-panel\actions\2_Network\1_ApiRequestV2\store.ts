import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';

import { ArgsInfoV2 } from './model';

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  const currentArgs = actionFlowStore.currentStep.args || {};
  const isNewAction = !currentArgs.name; // 判断是否为新增动作

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      apiId: '',
      description: '',
      headers: [] as FlowData[],
      params: [] as FlowData[],
      body: [] as FlowData[],
      outputs: [] as FlowData[],
      version: 'v2' as const,
      useRoot: isNewAction ? true : (currentArgs.useRoot ?? false), // 新增动作默认true，历史配置默认false
    } as ArgsInfoV2),
    ...currentArgs,
  }) as ArgsInfoV2;
};

export const useApiRequestV2Store = defineStore('ApiRequestV2', {
  state: () => {
    const state = {
      args: null,
      responseTemplate: [] as FlowData[], // 独立的响应体模板变量
    } as {
      args: ArgsInfoV2;
      responseTemplate: FlowData[];
    };
    initialState(state);
    return state;
  },
  getters: {
    variables() {
      return [
        {
          id: 'headers',
          key: 'headers',
          description: '请求头',
          type: 'object',
          children: this.args.headers || [],
        },
        {
          id: 'params',
          key: 'params',
          description: '请求参数',
          type: 'object',
          children: this.args.params || [],
        },
        {
          id: 'body',
          key: 'body',
          description: '请求体',
          type: 'object',
          children: this.args.body || [],
        },
        {
          id: 'result',
          key: 'result',
          description: '响应结果',
          type: 'object',
          value: {
            type: 'variable',
            variableType: 'current',
            variableValue: 'result',
          },
          // 使用独立的响应体模板变量，避免递归依赖
          children: this.responseTemplate || [],
        },
      ] as FlowData[];
    },
  },
  actions: {
    updateState() {
      initialState(this);
      // 初始化后检查是否需要加载响应体模板
      this.initializeResponseTemplate();
    },
    setArgs(args: ArgsInfoV2) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.setCurrentName(args.name);
      actionFlowStore.setCurrentArgs(args);

      // 更新结果数据
      this.updateResultData();
    },

    // 从API模板加载配置
    loadFromApiTemplate(template: any) {
      if (template.headers) {
        this.args.headers = cloneDeep(template.headers);
      }
      if (template.params) {
        this.args.params = cloneDeep(template.params);
      }
      if (template.body) {
        this.args.body = cloneDeep(template.body);
      }
      if (template.response) {
        this.args.outputs = cloneDeep(template.response);
      }
    },

    // 更新响应体模板（独立变量，避免递归依赖）
    updateResponseTemplate(template: FlowData[]) {
      this.responseTemplate = cloneDeep(template);
    },

    // 清空响应体模板
    clearResponseTemplate() {
      this.responseTemplate = [];
    },

    // 初始化所有模板（包括响应体模板和API模板）
    async initializeResponseTemplate() {
      // 1. 加载响应体模板
      if (this.args.responseId) {
        try {
          const { api, Services } = await import('@/api/system');
          const responseConfig = await api.run(Services.apiGetResponseById, { id: this.args.responseId });
          if (responseConfig && responseConfig.responseData) {
            const responseTemplate = JSON.parse(responseConfig.responseData);
            if (Array.isArray(responseTemplate)) {
              this.responseTemplate = cloneDeep(responseTemplate);
              // 同时更新outputs用于配置保存
              this.args.outputs = cloneDeep(responseTemplate);
              // 立即更新结果数据以显示输出参数
              this.updateResultData();
            }
          }
        } catch (error) {
          console.error('初始化响应体模板失败:', error);
        }
      }

      // 2. 如果有API ID且当前没有模板数据，加载API模板
      if (this.args.apiId && this.shouldLoadApiTemplate()) {
        try {
          const { api, Services } = await import('@/api/system');
          const apiInfo = await api.run(Services.apiGetApiById, { id: this.args.apiId });
          if (apiInfo) {
            await this.loadApiTemplatesForInitialization(apiInfo);
          }
        } catch (error) {
          console.error('初始化API模板失败:', error);
        }
      }
    },

    // 更新结果数据到actionFlowStore
    updateResultData() {
      const actionFlowStore = useActionFlowStore();

      // 设置输出参数
      if (this.args.useRoot) {
        // 使用根节点，直接绑定到根节点
        const resultNode = {
          id: 'result',
          key: 'result',
          description: '请求结果',
          type: 'object',
          children: this.args.outputs || [],
          value: {
            type: 'variable' as const,
            variableType: 'current' as const,
            variableValue: 'result',
          },
        };
        actionFlowStore.setStepResultData([resultNode]);
      } else {
        // 不使用根节点，使用输出配置
        actionFlowStore.setStepResultData(this.args.outputs || []);
      }
    },

    // 检查是否应该加载API模板（避免重复加载）
    shouldLoadApiTemplate() {
      return this.args.headers.length === 0 && this.args.params.length === 0 && this.args.body.length === 0;
    },

    // 为初始化加载API模板（不会重复添加，只在没有数据时加载）
    async loadApiTemplatesForInitialization(apiInfo: any) {
      const { recursionMergeVariable } = await import('@/components/action-panel/utils');

      try {
        // 加载请求头模板
        if (apiInfo.headers && this.args.headers.length === 0) {
          const headersTemplate = JSON.parse(apiInfo.headers);
          if (Array.isArray(headersTemplate)) {
            recursionMergeVariable(this.args.headers, headersTemplate);
          }
        }

        // 加载请求参数模板
        if (apiInfo.queryParameters && this.args.params.length === 0) {
          const paramsTemplate = JSON.parse(apiInfo.queryParameters);
          if (Array.isArray(paramsTemplate)) {
            recursionMergeVariable(this.args.params, paramsTemplate);
          }
        }

        // 加载请求体模板
        if (apiInfo.body && this.args.body.length === 0) {
          const bodyTemplate = JSON.parse(apiInfo.body);
          if (Array.isArray(bodyTemplate)) {
            recursionMergeVariable(this.args.body, bodyTemplate);
          }
        }
      } catch (error) {
        console.error('加载API模板失败:', error);
      }
    },
  },
});
